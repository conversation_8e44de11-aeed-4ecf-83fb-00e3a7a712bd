import yfinance as yf
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression

# 1) Download stock data for Infibeam Avenues (BSE ticker: INFIBEAM.BO)
data = yf.download("INFIBEAM.BO", start="2010-01-01", end="2025-08-18")

# 2) Create simple features
data["Return_1d"] = data["Close"].pct_change()        # daily return
data["MA_5"] = data["Close"].rolling(5).mean()        # 5-day moving avg
data["MA_20"] = data["Close"].rolling(20).mean()      # 20-day moving avg
data = data.dropna()

# 3) Create yearly label: 1 if last day close > first day close, else 0
years = sorted(set(data.index.year))
for y in years:
    year_data = data[data.index.year == y]
    if len(year_data) > 0:
        start_price = year_data.iloc[0]["Close"]
        end_price = year_data.iloc[-1]["Close"]
        label = 1 if end_price > start_price else 0
        data.loc[year_data.index, "Label"] = label

# Remove incomplete 2025 label (since year not finished)
data = data[data.index.year < 2025]

# 4) Features and labels
X = data[["Return_1d", "MA_5", "MA_20"]]
y = data["Label"]

# 5) Train/test split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)

# 6) Train model
model = LogisticRegression(max_iter=1000)
model.fit(X_train, y_train)

# 7) Evaluate
acc = model.score(X_test, y_test)
print("Backtest Accuracy:", acc)

# 8) Predict for full year 2025 (using today's data, Aug 18, 2025)
latest = yf.download("INFIBEAM.BO", start="2025-01-01", end="2025-08-18")
latest["Return_1d"] = latest["Close"].pct_change()
latest["MA_5"] = latest["Close"].rolling(5).mean()
latest["MA_20"] = latest["Close"].rolling(20).mean()
latest = latest.dropna()

latest_features = latest.iloc[-1][["Return_1d", "MA_5", "MA_20"]].values.reshape(1, -1)
pred = model.predict(latest_features)

print("Prediction for end of 2025:", "UP" if pred[0] == 1 else "DOWN")
