# BridgeLab_SIPP

This repository is part of **BridgeLabz_SIPP Java Training**, designed to help participants strengthen their Java foundations and enhance their professional development through structured topics, hands-on coding exercises, and real-world projects.


---

## 📘 Description

This repository includes:

- 📅 Daily assignments and learning logs  
- 🧠 Concept-based implementations and problem-solving solutions  
- 📒 Workshop notes and important resources  
- 🗂 Well-structured Java programs categorized into branches

### 📌 Training Roadmap:

- Core Java fundamentals  
- Object-Oriented Programming (OOP) principles  
- Exception handling  
- File handling  
- Collections framework  
- Java 8 features (Lambdas, Streams)  
- Final mini-project (integration of all concepts)

---

## 🔗 Live Branch Links

- [Core-Java-Programming](https://github.com/avaman7860gla/BridgeLab_SIPP/tree/Core-Java-Programming) –> Arrays, Control Flow, Extras, Java Programming Elements, Methods, and Strings  
- [OOPs-Fundamentals](https://github.com/avaman7860gla/BridgeLab_SIPP/tree/OOPs-Fundamentals) –> Classes and Objects, Constructors, static and final keywords, Object Modeling-Class Diagram, Sequence Diagram,  Inheritance, Pseudo codes(Day1, Day2, .....)
- [Workshop](https://github.com/avaman7860gla/BridgeLab_SIPP/tree/Workshop) –> Real-time tasks and practical exercises
- [DSA](https://github.com/avaman7860gla/BridgeLab_SIPP/tree/DSA) -> Stack, Queue, HashMap, Hashing Functions, Searching and Sorting Algorithms, LinkedList, Time-Complexity, Pseudo codes(Day-1, Day-2, .....)
- [Generics](https://github.com/avaman7860gla/BridgeLab_SIPP/tree/Generics) -> Problems related to generics, Collections-(List, Map, Queue, Set and Advanced Scenerio Based Problems), Java Streams-(file-handling), Regex, Pseudo codes(Day_1, Day_2, .....)

---

## 📂 File Structure

.gitignore         → Java-specific Git ignore rules  
README.md          → Overview and training documentation  

```
Branches:
  ├── Core-Java-Programming     → Core Java basics and exercises        
  ├── OOPs-Fundamentals         → Object-Oriented Programming concepts  
  ├── Workshop                  → Tasks and workshop-based exercises  
  ├── DSA                       → Data Structures and Algorithms in Java  
  ├── Generics                  → Java Generics with examples and use cases  

